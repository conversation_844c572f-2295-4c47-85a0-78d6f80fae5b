{"app": {"name": "Scenius.community", "tagline": "Build and grow your community"}, "nav": {"home": "Home", "dashboard": "Dashboard", "communities": "Communities", "spaces": "Spaces", "courses": "Courses", "forums": "Forums", "forumsDesc": "Discussion threads and conversations", "blogs": "Blogs", "blogsDesc": "Articles and long-form content", "messages": "Messages", "notifications": "Notifications", "settings": "Settings", "signIn": "Sign In", "signUp": "Sign Up", "profile": "Profile", "content": "Content", "analytics": "Analytics", "admin": "Admin"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "haveAccount": "Already have an account?"}, "dashboard": {"welcome": "Welcome back, {{name}}", "overview": "Overview", "overviewDescription": "Manage your communities, courses, and content", "communities": "Communities", "courses": "Courses", "messages": "Messages", "engagement": "Engagement", "yourCommunities": "Your Communities", "yourCourses": "Your Courses", "yourContent": "Your Content", "allCommunities": "All Communities", "allCourses": "All Courses", "recentActivity": "Recent Activity", "featuredContent": "Featured Content", "viewAll": "View All", "noCommunities": "No communities yet", "activityComingSoon": "Recent activity feed coming soon.", "coursesComingSoon": "Courses section coming soon.", "contentFeedComingSoon": "Content feed coming soon."}, "community": {"createNew": "Create New Community", "name": "Community Name", "namePlaceholder": "Enter community name", "nameRequired": "Community name is required", "slug": "URL Slug", "slugPlaceholder": "your-community-name", "slugRequired": "Slug is required", "slugInvalid": "Slug can only contain lowercase letters, numbers, and hyphens", "slugHelp": "This will be used in the URL of your community", "description": "Description", "descriptionPlaceholder": "Describe what your community is about", "descriptionRequired": "Description is required", "type": "Community Type", "typeForum": "Discussion Forum", "typeForumDesc": "A community focused on discussions and Q&A", "typeLearning": "Learning Community", "typeLearningDesc": "A community focused on courses and learning materials", "template": "Community Template", "selectTemplate": "Select a template", "templateBasic": "Basic Community", "templateLearning": "Learning Platform", "templateForum": "Discussion Forum", "templateHelp": "Templates provide pre-configured spaces and settings", "privacy": "Privacy", "public": "Public Community", "publicDescription": "Anyone can see and join your community", "private": "Private Community", "privateDescription": "Only approved members can see and join your community", "logo": "Community Logo", "uploadLogo": "Upload logo image", "logoUploaded": "Logo uploaded", "logoHelp": "Recommended size: 200x200px, max 2MB", "banner": "Community Banner", "uploadBanner": "Upload banner image", "bannerUploaded": "Banner uploaded", "bannerHelp": "Recommended size: 1200x300px, max 5MB", "createDraft": "Create as Draft", "createAndPublish": "Create & Publish", "createSuccess": "Community created", "createSuccessDescription": "Your community has been created successfully", "createError": "Error creating community", "toast": {"creationErrorTitle": "Community Creation Failed", "creationSuccessTitle": "Community Created!", "creationSuccessDesc": "Community created successfully", "networkErrorTitle": "Network Error"}, "error": {"unexpected": "An unexpected error occurred"}}, "course": {"enrolled": "Enrolled", "progress": "Progress", "lessons": "{{count}} lessons", "continue": "Continue", "create": "Create Course"}, "content": {"post": "Post", "article": "Article", "course": "Course", "comment": "Comment", "like": "Like", "share": "Share", "save": "Save"}, "common": {"loading": "Loading...", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search..."}, "footer": {"product": "Product", "features": "Features", "pricing": "Pricing", "testimonials": "Testimonials", "company": "Company", "about": "About", "blog": "Blog", "contact": "Contact", "legal": "Legal", "privacy": "Privacy", "terms": "Terms"}, "communities": {"title": "Communities", "description": "Discover and join communities", "searchPlaceholder": "Search communities...", "myCommunities": "My Communities", "discover": "Discover", "noSearchResults": "No communities found", "noCommunities": "You haven't joined any communities yet", "noPublicCommunities": "No public communities available"}, "settings": {"accountSettings": "Account <PERSON><PERSON>", "profileUpdated": "Profile updated successfully", "updateError": "Error updating profile", "passwordUpdated": "Password updated successfully", "passwordMismatch": "Passwords do not match", "notificationsSaved": "Notification preferences saved", "savePreferences": "Save Preferences", "notificationPreferences": "Notification Preferences", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications"}}