import { Input as ChakraInput } from "@chakra-ui/react"
import * as React from "react"

export interface InputProps extends ChakraInput.RootProps {}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  function Input(props, ref) {
    return <ChakraInput.Root ref={ref} {...props} />
  },
)

// Re-export the InputGroup from the proper component
export { InputGroup } from "./input-group"

// For backward compatibility, create simple element components
export interface InputLeftElementProps extends React.HTMLAttributes<HTMLDivElement> {
  pointerEvents?: "none" | "auto"
}

export const InputLeftElement = React.forwardRef<HTMLDivElement, InputLeftElementProps>(
  function InputLeftElement({ children, ...props }, ref) {
    return (
      <div ref={ref} {...props}>
        {children}
      </div>
    )
  },
)

export interface InputRightElementProps extends React.HTMLAttributes<HTMLDivElement> {
  pointerEvents?: "none" | "auto"
}

export const InputRightElement = React.forwardRef<HTMLDivElement, InputRightElementProps>(
  function InputRightElement({ children, ...props }, ref) {
    return (
      <div ref={ref} {...props}>
        {children}
      </div>
    )
  },
)
