"use client"

import { Input as ChakraInput, Group } from "@chakra-ui/react"
import { forwardRef } from "react"

export interface InputProps extends ChakraInput.RootProps {}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  function Input(props, ref) {
    return <ChakraInput.Root ref={ref} {...props} />
  },
)

export interface InputGroupProps extends React.HTMLAttributes<HTMLDivElement> {}

export const InputGroup = forwardRef<HTMLDivElement, InputGroupProps>(
  function InputGroup(props, ref) {
    return <Group ref={ref} flex="1" isolate {...props} />
  },
)

export interface InputLeftElementProps extends ChakraInput.LeftElementProps {}

export const InputLeftElement = forwardRef<HTMLDivElement, InputLeftElementProps>(
  function InputLeftElement(props, ref) {
    return <ChakraInput.LeftElement ref={ref} {...props} />
  },
)

export interface InputRightElementProps extends ChakraInput.RightElementProps {}

export const InputRightElement = forwardRef<HTMLDivElement, InputRightElementProps>(
  function InputRightElement(props, ref) {
    return <ChakraInput.RightElement ref={ref} {...props} />
  },
)

export interface InputLeftAddonProps extends ChakraInput.LeftAddonProps {}

export const InputLeftAddon = forwardRef<HTMLDivElement, InputLeftAddonProps>(
  function InputLeftAddon(props, ref) {
    return <ChakraInput.LeftAddon ref={ref} {...props} />
  },
)

export interface InputRightAddonProps extends ChakraInput.RightAddonProps {}

export const InputRightAddon = forwardRef<HTMLDivElement, InputRightAddonProps>(
  function InputRightAddon(props, ref) {
    return <ChakraInput.RightAddon ref={ref} {...props} />
  },
)
