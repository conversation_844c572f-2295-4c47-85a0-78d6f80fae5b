"use client"

import { Box } from "@chakra-ui/react"
import { forwardRef } from "react"

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

export function Input(props: InputProps) {
  return (
    <Box
      as="input"
      px="3"
      py="2"
      border="1px solid"
      borderColor="gray.200"
      borderRadius="md"
      bg="white"
      _focus={{
        borderColor: "blue.500",
        boxShadow: "0 0 0 1px var(--chakra-colors-blue-500)",
        outline: "none"
      }}
      _hover={{
        borderColor: "gray.300"
      }}
      {...props}
    />
  )
}

export interface InputGroupProps extends React.HTMLAttributes<HTMLDivElement> {}

export function InputGroup(props: InputGroupProps) {
  return <Box position="relative" {...props} />
}

// Simplified components for v3 compatibility
export interface InputLeftElementProps extends React.HTMLAttributes<HTMLDivElement> {
  pointerEvents?: "none" | "auto"
}

export function InputLeftElement({ pointerEvents = "none", ...props }: InputLeftElementProps) {
  return (
    <Box
      position="absolute"
      left="3"
      top="50%"
      transform="translateY(-50%)"
      zIndex="1"
      pointerEvents={pointerEvents}
      {...props}
    />
  )
}

export interface InputRightElementProps extends React.HTMLAttributes<HTMLDivElement> {
  pointerEvents?: "none" | "auto"
}

export const InputRightElement = forwardRef<HTMLDivElement, InputRightElementProps>(
  function InputRightElement({ pointerEvents = "none", ...props }, ref) {
    return (
      <Box
        ref={ref}
        position="absolute"
        right="3"
        top="50%"
        transform="translateY(-50%)"
        zIndex="1"
        pointerEvents={pointerEvents}
        {...props}
      />
    )
  },
)

// Simple addon components for compatibility
export interface InputLeftAddonProps extends React.HTMLAttributes<HTMLDivElement> {}

export const InputLeftAddon = forwardRef<HTMLDivElement, InputLeftAddonProps>(
  function InputLeftAddon(props, ref) {
    return (
      <Box
        ref={ref}
        px="3"
        py="2"
        bg="gray.50"
        border="1px solid"
        borderColor="gray.200"
        borderRightWidth="0"
        borderTopLeftRadius="md"
        borderBottomLeftRadius="md"
        {...props}
      />
    )
  },
)

export interface InputRightAddonProps extends React.HTMLAttributes<HTMLDivElement> {}

export const InputRightAddon = forwardRef<HTMLDivElement, InputRightAddonProps>(
  function InputRightAddon(props, ref) {
    return (
      <Box
        ref={ref}
        px="3"
        py="2"
        bg="gray.50"
        border="1px solid"
        borderColor="gray.200"
        borderLeftWidth="0"
        borderTopRightRadius="md"
        borderBottomRightRadius="md"
        {...props}
      />
    )
  },
)
