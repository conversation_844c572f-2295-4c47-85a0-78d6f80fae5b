"use client"

import { Input as ChakraInput, Group } from "@chakra-ui/react"
import { forwardRef } from "react"

export interface InputProps extends ChakraInput.RootProps {
  startElement?: React.ReactNode
  endElement?: React.ReactNode
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  function Input(props, ref) {
    const { startElement, endElement, children, ...rest } = props
    return (
      <Group flex="1" isolate>
        {startElement && (
          <ChakraInput.LeftElement pointerEvents="none">
            {startElement}
          </ChakraInput.LeftElement>
        )}
        <ChakraInput.Root ref={ref} {...rest}>
          {children}
        </ChakraInput.Root>
        {endElement && (
          <ChakraInput.RightElement pointerEvents="none">
            {endElement}
          </ChakraInput.RightElement>
        )}
      </Group>
    )
  },
)

export const InputGroup = Group
export const InputLeftElement = ChakraInput.LeftElement
export const InputRightElement = ChakraInput.RightElement
export const InputLeftAddon = ChakraInput.LeftAddon
export const InputRightAddon = ChakraInput.RightAddon
