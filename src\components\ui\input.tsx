import { Input as ChakraInput } from "@chakra-ui/react"
import * as React from "react"

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  function Input(props, ref) {
    return <ChakraInput ref={ref} {...props} />
  },
)

// Re-export the InputGroup from the proper component
export { InputGroup } from "./input-group"

// For backward compatibility, create simple element components
export interface InputLeftElementProps extends React.HTMLAttributes<HTMLDivElement> {
  pointerEvents?: "none" | "auto"
}

export const InputLeftElement = React.forwardRef<HTMLDivElement, InputLeftElementProps>(
  function InputLeftElement({ children, ...props }, ref) {
    return (
      <div ref={ref} {...props}>
        {children}
      </div>
    )
  },
)

export interface InputRightElementProps extends React.HTMLAttributes<HTMLDivElement> {
  pointerEvents?: "none" | "auto"
}

export const InputRightElement = React.forwardRef<HTMLDivElement, InputRightElementProps>(
  function InputRightElement({ children, ...props }, ref) {
    return (
      <div ref={ref} {...props}>
        {children}
      </div>
    )
  },
)

// Add InputLeftAddon and InputRightAddon for backward compatibility
export interface InputLeftAddonProps extends React.HTMLAttributes<HTMLDivElement> {}

export const InputLeftAddon = React.forwardRef<HTMLDivElement, InputLeftAddonProps>(
  function InputLeftAddon({ children, ...props }, ref) {
    return (
      <div ref={ref} {...props}>
        {children}
      </div>
    )
  },
)

export interface InputRightAddonProps extends React.HTMLAttributes<HTMLDivElement> {}

export const InputRightAddon = React.forwardRef<HTMLDivElement, InputRightAddonProps>(
  function InputRightAddon({ children, ...props }, ref) {
    return (
      <div ref={ref} {...props}>
        {children}
      </div>
    )
  },
)
