"use client";

import { useState } from "react";
import {
  Box,
  Heading,
  Text,
  Flex,
  Button,
  Container,
  SimpleGrid,
  Card,
  CardBody,
  Avatar,
  Badge,
  Link,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  useColorModeValue,
  useDisclosure,
  Image,
  Tag,
  HStack,
  Divider,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import NextLink from "next/link";
import { formatDistanceToNow } from "date-fns";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Search, Plus, MessageSquare, ThumbsUp, Filter } from "lucide-react";
import { supabase } from "@/lib/supabase/client";
import LanguageSwitcher from "@/components/auth/LanguageSwitcher";

interface BlogSpaceProps {
  space: any;
  posts: any[];
  currentUser: any;
  isMember: boolean;
}

export default function BlogSpace({
  space,
  posts: initialPosts,
  currentUser,
  isMember,
}: BlogSpaceProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { direction } = useDirection();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [posts, setPosts] = useState(initialPosts);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [sortBy, setSortBy] = useState("recent");

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  // Extract unique categories from posts
  const categories = Array.from(
    new Set(posts.map((post) => post.metadata?.category).filter(Boolean)),
  );

  // Filter and sort posts
  const filteredPosts = posts
    .filter((post) => {
      // Search filter
      const matchesSearch = !searchQuery
        ? true
        : post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (post.content.text &&
            post.content.text
              .toLowerCase()
              .includes(searchQuery.toLowerCase()));

      // Category filter
      const matchesCategory = !categoryFilter
        ? true
        : post.metadata?.category === categoryFilter;

      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      if (sortBy === "recent") {
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      } else if (sortBy === "popular") {
        const aEngagement =
          (a.reactions?.[0]?.count || 0) + (a.comments?.[0]?.count || 0);
        const bEngagement =
          (b.reactions?.[0]?.count || 0) + (b.comments?.[0]?.count || 0);
        return bEngagement - aEngagement;
      } else if (sortBy === "featured") {
        // Featured posts first, then by date
        if (a.status === "featured" && b.status !== "featured") return -1;
        if (a.status !== "featured" && b.status === "featured") return 1;
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      }
      return 0;
    });

  // Get featured posts
  const featuredPosts = posts.filter((post) => post.status === "featured");

  // Determine layout based on space settings
  const blogLayout = space.settings?.blogLayout || "grid";

  const handleCreatePost = () => {
    router.push(`/spaces/${space.id}/create-post`);
  };

  return (
    <Box dir={direction}>
      <Container maxW="container.xl" py={6}>
        <Flex justify="flex-end" mb={4}>
          <LanguageSwitcher />
        </Flex>
        {/* Blog Header */}
        <Box
          bg={bgColor}
          borderRadius="xl"
          borderWidth="1px"
          borderColor={borderColor}
          p={6}
          mb={6}
        >
          <Flex justify="space-between" align="flex-start" mb={4}>
            <Box>
              <Heading as="h1" size="xl" mb={2}>
                {space.name}
              </Heading>
              <Text color="gray.600" fontSize="lg">
                {space.description}
              </Text>
            </Box>
            {isMember && (
              <Button
                leftIcon={<Plus size={16} />}
                onClick={handleCreatePost}
                colorScheme="teal"
              >
                {t("blog.createPost", "Create Post")}
              </Button>
            )}
          </Flex>
        </Box>

        {/* Featured Posts (if any and using magazine layout) */}
        {blogLayout === "magazine" && featuredPosts.length > 0 && (
          <Box mb={8}>
            <Heading as="h2" size="lg" mb={4}>
              {t("blog.featured", "Featured Posts")}
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              {featuredPosts.slice(0, 2).map((post) => (
                <Card key={post.id} overflow="hidden" height="100%">
                  <Flex direction={{ base: "column", sm: "row" }} height="100%">
                    {post.content?.image && (
                      <Box
                        width={{ base: "100%", sm: "40%" }}
                        height={{ base: "200px", sm: "auto" }}
                      >
                        <Image
                          src={post.content.image}
                          alt={post.title}
                          objectFit="cover"
                          width="100%"
                          height="100%"
                        />
                      </Box>
                    )}
                    <CardBody>
                      {post.metadata?.category && (
                        <Badge colorPalette="teal" mb={2}>
                          {post.metadata.category}
                        </Badge>
                      )}
                      <Heading as="h3" size="md" mb={2}>
                        <Link
                          as={NextLink}
                          href={`/spaces/${space.id}/posts/${post.id}`}
                          _hover={{ textDecoration: "underline" }}
                        >
                          {post.title}
                        </Link>
                      </Heading>
                      <Flex align="center" gap={2} mb={2}>
                        <Avatar
                          size="xs"
                          name={post.author?.full_name}
                          src={post.author?.avatar_url}
                        />
                        <Text fontSize="sm">{post.author?.full_name}</Text>
                        <Text fontSize="xs" color="gray.500">
                          {formatDistanceToNow(new Date(post.created_at), {
                            addSuffix: true,
                          })}
                        </Text>
                      </Flex>
                      <Text lineClamp={3} mb={4}>
                        {typeof post.content === "string"
                          ? post.content
                          : post.content.text || ""}
                      </Text>

                      {/* Tags */}
                      {post.metadata?.tags && post.metadata.tags.length > 0 && (
                        <HStack spacing={2} mb={4} flexWrap="wrap">
                          {post.metadata.tags.slice(0, 3).map((tag, index) => (
                            <Tag key={index} size="sm" variant="subtle">
                              {tag}
                            </Tag>
                          ))}
                        </HStack>
                      )}

                      <Flex align="center" gap={4}>
                        <Flex align="center" gap={1}>
                          <ThumbsUp size={16} />
                          <Text fontSize="sm">
                            {post.reactions?.[0]?.count || 0}
                          </Text>
                        </Flex>
                        <Flex align="center" gap={1}>
                          <MessageSquare size={16} />
                          <Text fontSize="sm">
                            {post.comments?.[0]?.count || 0}
                          </Text>
                        </Flex>
                      </Flex>
                    </CardBody>
                  </Flex>
                </Card>
              ))}
            </SimpleGrid>
          </Box>
        )}

        {/* Filters and Search */}
        <Flex
          direction={{ base: "column", md: "row" }}
          justify="space-between"
          align={{ base: "stretch", md: "center" }}
          mb={6}
          gap={4}
        >
          <InputGroup maxW={{ base: "full", md: "320px" }}>
            <InputLeftElement pointerEvents="none">
              <Search size={18} color="gray.300" />
            </InputLeftElement>
            <Input
              placeholder={t("blog.searchPlaceholder", "Search posts...")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </InputGroup>

          <Flex gap={4}>
            {categories.length > 0 && (
              <Select
                placeholder={t("blog.allCategories", "All Categories")}
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                width={{ base: "full", md: "auto" }}
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </Select>
            )}

            <Select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              width={{ base: "full", md: "auto" }}
            >
              <option value="recent">
                {t("blog.sortRecent", "Most Recent")}
              </option>
              <option value="popular">
                {t("blog.sortPopular", "Most Popular")}
              </option>
              <option value="featured">
                {t("blog.sortFeatured", "Featured First")}
              </option>
            </Select>
          </Flex>
        </Flex>

        {/* Blog Posts */}
        {filteredPosts.length > 0 ? (
          blogLayout === "grid" ? (
            // Grid Layout
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {filteredPosts.map((post) => (
                <Card key={post.id} overflow="hidden" height="100%">
                  {post.content?.image && (
                    <Box height="200px" overflow="hidden">
                      <Image
                        src={post.content.image}
                        alt={post.title}
                        objectFit="cover"
                        width="100%"
                        height="100%"
                      />
                    </Box>
                  )}
                  <CardBody>
                    {post.metadata?.category && (
                      <Badge colorPalette="teal" mb={2}>
                        {post.metadata.category}
                      </Badge>
                    )}
                    <Heading as="h3" size="md" mb={2}>
                      <Link
                        as={NextLink}
                        href={`/spaces/${space.id}/posts/${post.id}`}
                        _hover={{ textDecoration: "underline" }}
                      >
                        {post.title}
                      </Link>
                    </Heading>
                    <Flex align="center" gap={2} mb={2}>
                      <Avatar
                        size="xs"
                        name={post.author?.full_name}
                        src={post.author?.avatar_url}
                      />
                      <Text fontSize="sm">{post.author?.full_name}</Text>
                    </Flex>
                    <Text color="gray.500" fontSize="sm" mb={2}>
                      {formatDistanceToNow(new Date(post.created_at), {
                        addSuffix: true,
                      })}
                    </Text>
                    <Text lineClamp={3} mb={4}>
                      {typeof post.content === "string"
                        ? post.content
                        : post.content.text || ""}
                    </Text>

                    <Divider mb={4} />

                    <Flex justify="space-between">
                      <Flex align="center" gap={1}>
                        <ThumbsUp size={16} />
                        <Text fontSize="sm">
                          {post.reactions?.[0]?.count || 0}
                        </Text>
                      </Flex>
                      <Flex align="center" gap={1}>
                        <MessageSquare size={16} />
                        <Text fontSize="sm">
                          {post.comments?.[0]?.count || 0}
                        </Text>
                      </Flex>
                    </Flex>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          ) : blogLayout === "list" ? (
            // List Layout
            <Box>
              {filteredPosts.map((post) => (
                <Card key={post.id} mb={6} overflow="hidden">
                  <Flex direction={{ base: "column", md: "row" }}>
                    {post.content?.image && (
                      <Box
                        width={{ base: "100%", md: "30%" }}
                        height={{ base: "200px", md: "auto" }}
                      >
                        <Image
                          src={post.content.image}
                          alt={post.title}
                          objectFit="cover"
                          width="100%"
                          height="100%"
                        />
                      </Box>
                    )}
                    <CardBody>
                      <Flex justify="space-between" align="flex-start" mb={2}>
                        <Box>
                          {post.metadata?.category && (
                            <Badge colorScheme="teal" mb={2}>
                              {post.metadata.category}
                            </Badge>
                          )}
                          <Heading as="h3" size="md" mb={2}>
                            <Link
                              as={NextLink}
                              href={`/spaces/${space.id}/posts/${post.id}`}
                              _hover={{ textDecoration: "underline" }}
                            >
                              {post.title}
                            </Link>
                          </Heading>
                        </Box>
                        <Text color="gray.500" fontSize="sm">
                          {formatDistanceToNow(new Date(post.created_at), {
                            addSuffix: true,
                          })}
                        </Text>
                      </Flex>

                      <Flex align="center" gap={2} mb={3}>
                        <Avatar
                          size="xs"
                          name={post.author?.full_name}
                          src={post.author?.avatar_url}
                        />
                        <Text fontSize="sm">{post.author?.full_name}</Text>
                      </Flex>

                      <Text noOfLines={3} mb={4}>
                        {typeof post.content === "string"
                          ? post.content
                          : post.content.text || ""}
                      </Text>

                      {/* Tags */}
                      {post.metadata?.tags && post.metadata.tags.length > 0 && (
                        <HStack spacing={2} mb={4} flexWrap="wrap">
                          {post.metadata.tags.slice(0, 5).map((tag, index) => (
                            <Tag key={index} size="sm" variant="subtle">
                              {tag}
                            </Tag>
                          ))}
                        </HStack>
                      )}

                      <Flex justify="flex-start" gap={6}>
                        <Flex align="center" gap={1}>
                          <ThumbsUp size={16} />
                          <Text fontSize="sm">
                            {post.reactions?.[0]?.count || 0}
                          </Text>
                        </Flex>
                        <Flex align="center" gap={1}>
                          <MessageSquare size={16} />
                          <Text fontSize="sm">
                            {post.comments?.[0]?.count || 0}
                          </Text>
                        </Flex>
                      </Flex>
                    </CardBody>
                  </Flex>
                </Card>
              ))}
            </Box>
          ) : (
            // Magazine Layout (default fallback)
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {filteredPosts.map((post) => (
                <Card key={post.id} overflow="hidden" height="100%">
                  {post.content?.image && (
                    <Box height="200px" overflow="hidden">
                      <Image
                        src={post.content.image}
                        alt={post.title}
                        objectFit="cover"
                        width="100%"
                        height="100%"
                      />
                    </Box>
                  )}
                  <CardBody>
                    {post.metadata?.category && (
                      <Badge colorScheme="teal" mb={2}>
                        {post.metadata.category}
                      </Badge>
                    )}
                    <Heading as="h3" size="md" mb={2}>
                      <Link
                        as={NextLink}
                        href={`/spaces/${space.id}/posts/${post.id}`}
                        _hover={{ textDecoration: "underline" }}
                      >
                        {post.title}
                      </Link>
                    </Heading>
                    <Flex align="center" gap={2} mb={2}>
                      <Avatar
                        size="xs"
                        name={post.author?.full_name}
                        src={post.author?.avatar_url}
                      />
                      <Text fontSize="sm">{post.author?.full_name}</Text>
                    </Flex>
                    <Text color="gray.500" fontSize="sm" mb={2}>
                      {formatDistanceToNow(new Date(post.created_at), {
                        addSuffix: true,
                      })}
                    </Text>
                    <Text noOfLines={3} mb={4}>
                      {typeof post.content === "string"
                        ? post.content
                        : post.content.text || ""}
                    </Text>

                    <Divider mb={4} />

                    <Flex justify="space-between">
                      <Flex align="center" gap={1}>
                        <ThumbsUp size={16} />
                        <Text fontSize="sm">
                          {post.reactions?.[0]?.count || 0}
                        </Text>
                      </Flex>
                      <Flex align="center" gap={1}>
                        <MessageSquare size={16} />
                        <Text fontSize="sm">
                          {post.comments?.[0]?.count || 0}
                        </Text>
                      </Flex>
                    </Flex>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          )
        ) : (
          <Box
            textAlign="center"
            py={10}
            borderWidth="1px"
            borderRadius="lg"
            borderColor={borderColor}
          >
            <Text>
              {t(
                "blog.noPosts",
                "No posts found. Try adjusting your filters or create a new post.",
              )}
            </Text>
            {isMember && (
              <Button
                leftIcon={<Plus size={16} />}
                onClick={handleCreatePost}
                colorScheme="teal"
                mt={4}
              >
                {t("blog.createPost", "Create Post")}
              </Button>
            )}
          </Box>
        )}
      </Container>
    </Box>
  );
}
