"use client";

import { useState } from "react";
import {
  Button,
  Input,
  Textarea,
  Switch,
  Box,
  Text,
  Flex,
  Icon,
  InputGroup,
  Select,
} from "@chakra-ui/react";
import { <PERSON>dal, ModalOverlay, <PERSON>dalContent, <PERSON>dal<PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalCloseButton } from "@/components/ui/modal";
import { FormControl, FormLabel, FormHelperText, FormErrorMessage } from "@/components/ui/form";
import { InputLeftAddon } from "@/components/ui/input";
import { useColorModeValue } from "@/components/ui/color-mode";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase/client";
import { toaster } from "@/components/ui/toaster";
import {
  Globe,
  Lock,
  Image,
  Upload,
  MessageSquare,
  BookOpen,
} from "lucide-react";

interface CreateCommunityModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CreateCommunityModal({
  isOpen,
  onClose,
}: CreateCommunityModalProps) {
  const { t } = useTranslation();
  const router = useRouter();

  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");
  const [description, setDescription] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setName(value);

    // Auto-generate slug from name
    if (!slug || slug === generateSlug(name)) {
      const newSlug = generateSlug(value);
      setSlug(newSlug);
    }
  };

  const generateSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^\w\-]+/g, "")
      .replace(/\-\-+/g, "-")
      .replace(/^-+/, "")
      .replace(/-+$/, "");
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!name.trim()) {
      newErrors.name = t(
        "community.nameRequired",
        "Community name is required",
      );
    }

    if (!slug.trim()) {
      newErrors.slug = t("community.slugRequired", "Slug is required");
    } else if (!/^[a-z0-9-]+$/.test(slug)) {
      newErrors.slug = t(
        "community.slugInvalid",
        "Slug can only contain lowercase letters, numbers, and hyphens",
      );
    }

    if (!description.trim()) {
      newErrors.description = t(
        "community.descriptionRequired",
        "Description is required",
      );
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Get current user
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session) {
        throw new Error("You must be logged in to create a community");
      }

      // Create community
      const { data: community, error: communityError } = await supabase
        .from("communities")
        .insert([
          {
            name,
            slug,
            description,
            is_private: isPrivate,
            owner_id: session.user.id,
            default_language: "en",
            supports_rtl: true,
            logo_url: logoFile
              ? "https://api.dicebear.com/7.x/identicon/svg?seed=" + slug
              : null,
            banner_url: bannerFile
              ? "https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=800&q=80"
              : null,
          },
        ])
        .select()
        .single();

      if (communityError) throw communityError;

      // Add creator as community member with admin role
      const { error: memberError } = await supabase
        .from("community_members")
        .insert([
          {
            community_id: community.id,
            profile_id: session.user.id,
            role: "admin",
          },
        ]);

      if (memberError) throw memberError;

      // Create default forum space
      const { error: spaceError } = await supabase.from("spaces").insert([
        {
          community_id: community.id,
          name: "General Discussion",
          slug: "general-discussion",
          description:
            "A place to discuss general topics related to our community.",
          type: "forum",
          is_private: false,
          order_index: 0,
        },
      ]);

      if (spaceError) throw spaceError;

      toaster.success({
        title: t("community.createSuccess", "Community created"),
        description: t(
          "community.createSuccessDescription",
          "Your community has been created successfully",
        ),
        duration: 5000,
      });

      // Close modal and refresh
      onClose();
      router.refresh();

      // Navigate to the new community dashboard (in a real app)
      // router.push(`/communities/${community.slug}`);
    } catch (error: any) {
      toaster.error({
        title: t("community.createError", "Error creating community"),
        description: error.message,
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {t("community.createNew", "Create New Community")}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <FormControl isInvalid={!!errors.name} mb={4}>
            <FormLabel>{t("community.name", "Community Name")}</FormLabel>
            <Input
              value={name}
              onChange={handleNameChange}
              placeholder={t(
                "community.namePlaceholder",
                "Enter community name",
              )}
            />
            <FormErrorMessage>{errors.name}</FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.slug} mb={4}>
            <FormLabel>{t("community.slug", "URL Slug")}</FormLabel>
            <InputGroup>
              <InputLeftAddon>scenius.community/</InputLeftAddon>
              <Input
                value={slug}
                onChange={(e) => setSlug(e.target.value)}
                placeholder={t(
                  "community.slugPlaceholder",
                  "your-community-name",
                )}
              />
            </InputGroup>
            <FormHelperText>
              {t(
                "community.slugHelp",
                "This will be used in the URL of your community",
              )}
            </FormHelperText>
            <FormErrorMessage>{errors.slug}</FormErrorMessage>
          </FormControl>

          <FormControl mb={4}>
            <FormLabel>{t("community.type", "Community Type")}</FormLabel>
            <Flex
              direction="column"
              gap={3}
              p={4}
              borderWidth="1px"
              borderRadius="md"
              borderColor={borderColor}
            >
              <Flex
                align="center"
                p={3}
                borderRadius="md"
                bg={bgColor}
                borderWidth="1px"
                borderColor={borderColor}
                cursor="pointer"
                _hover={{ borderColor: "wuilt.product" }}
              >
                <Icon as={MessageSquare} boxSize={5} mr={3} />
                <Box>
                  <Text fontWeight="medium">
                    {t("community.typeForum", "Discussion Forum")}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    {t(
                      "community.typeForumDesc",
                      "A community focused on discussions and Q&A",
                    )}
                  </Text>
                </Box>
              </Flex>

              <Flex
                align="center"
                p={3}
                borderRadius="md"
                bg={bgColor}
                borderWidth="1px"
                borderColor={borderColor}
                cursor="pointer"
                _hover={{ borderColor: "wuilt.product" }}
              >
                <Icon as={BookOpen} boxSize={5} mr={3} />
                <Box>
                  <Text fontWeight="medium">
                    {t("community.typeLearning", "Learning Community")}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    {t(
                      "community.typeLearningDesc",
                      "A community focused on courses and learning materials",
                    )}
                  </Text>
                </Box>
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mb={4}>
            <FormLabel>
              {t("community.template", "Community Template")}
            </FormLabel>
            <Select
              placeholder={t("community.selectTemplate", "Select a template")}
            >
              <option value="basic">
                {t("community.templateBasic", "Basic Community")}
              </option>
              <option value="learning">
                {t("community.templateLearning", "Learning Platform")}
              </option>
              <option value="forum">
                {t("community.templateForum", "Discussion Forum")}
              </option>
            </Select>
            <FormHelperText>
              {t(
                "community.templateHelp",
                "Templates provide pre-configured spaces and settings",
              )}
            </FormHelperText>
          </FormControl>

          <FormControl isInvalid={!!errors.description} mb={4}>
            <FormLabel>{t("community.description", "Description")}</FormLabel>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={t(
                "community.descriptionPlaceholder",
                "Describe what your community is about",
              )}
              rows={3}
            />
            <FormErrorMessage>{errors.description}</FormErrorMessage>
          </FormControl>

          <FormControl mb={4}>
            <FormLabel>{t("community.privacy", "Privacy")}</FormLabel>
            <Flex
              direction="column"
              gap={3}
              p={4}
              borderWidth="1px"
              borderRadius="md"
              borderColor={borderColor}
            >
              <Flex
                align="center"
                p={3}
                borderRadius="md"
                bg={!isPrivate ? "wuilt.teal50" : bgColor}
                borderWidth="1px"
                borderColor={!isPrivate ? "wuilt.product" : borderColor}
                cursor="pointer"
                onClick={() => setIsPrivate(false)}
              >
                <Icon as={Globe} boxSize={5} mr={3} />
                <Box>
                  <Text fontWeight="medium">
                    {t("community.public", "Public Community")}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    {t(
                      "community.publicDescription",
                      "Anyone can see and join your community",
                    )}
                  </Text>
                </Box>
              </Flex>

              <Flex
                align="center"
                p={3}
                borderRadius="md"
                bg={isPrivate ? "wuilt.teal50" : bgColor}
                borderWidth="1px"
                borderColor={isPrivate ? "wuilt.product" : borderColor}
                cursor="pointer"
                onClick={() => setIsPrivate(true)}
              >
                <Icon as={Lock} boxSize={5} mr={3} />
                <Box>
                  <Text fontWeight="medium">
                    {t("community.private", "Private Community")}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    {t(
                      "community.privateDescription",
                      "Only approved members can see and join your community",
                    )}
                  </Text>
                </Box>
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mb={4}>
            <FormLabel>{t("community.logo", "Community Logo")}</FormLabel>
            <Button
              leftIcon={<Upload size={16} />}
              onClick={() => {}}
              variant="outline"
              w="full"
              py={6}
            >
              {logoFile
                ? t("community.logoUploaded", "Logo uploaded")
                : t("community.uploadLogo", "Upload logo image")}
            </Button>
            <FormHelperText>
              {t("community.logoHelp", "Recommended size: 200x200px, max 2MB")}
            </FormHelperText>
          </FormControl>

          <FormControl mb={4}>
            <FormLabel>{t("community.banner", "Community Banner")}</FormLabel>
            <Button
              leftIcon={<Image size={16} />}
              onClick={() => {}}
              variant="outline"
              w="full"
              py={6}
            >
              {bannerFile
                ? t("community.bannerUploaded", "Banner uploaded")
                : t("community.uploadBanner", "Upload banner image")}
            </Button>
            <FormHelperText>
              {t(
                "community.bannerHelp",
                "Recommended size: 1200x300px, max 5MB",
              )}
            </FormHelperText>
          </FormControl>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            {t("common.cancel", "Cancel")}
          </Button>
          <Button
            variant="outline"
            colorScheme="teal"
            mr={3}
            onClick={handleSubmit}
            isLoading={isSubmitting}
          >
            {t("community.createDraft", "Create as Draft")}
          </Button>
          <Button
            colorScheme="teal"
            onClick={() => {
              // Set to publish immediately
              handleSubmit();
            }}
            isLoading={isSubmitting}
          >
            {t("community.createAndPublish", "Create & Publish")}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
