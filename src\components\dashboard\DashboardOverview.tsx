"use client";

import React from "react";
import {
  Box,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Button,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Flex,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useColorModeValue,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useDirection } from "@/lib/contexts/DirectionContext";
import {
  Users,
  BookOpen,
  MessageSquare,
  TrendingUp,
  Plus,
  ChevronRight,
} from "lucide-react";
import CommunityCard from "@/components/community/CommunityCard";
// import CourseCard from "@/components/courses/CourseCard"; // Commented out as it's not fully used yet
// import ContentFeed from "@/components/content/ContentFeed"; // Commented out as it's not fully used yet
import type { CommunityForDashboardView } from "@/types/app.types"; // Import from app.types

interface DashboardOverviewProps {
  userName: string;
  language: string;
  communities: CommunityForDashboardView[];
}

const DashboardOverview = ({
  userName,
  language,
  communities = [],
}: DashboardOverviewProps) => {
  const { t } = useTranslation();
  const { direction } = useDirection();
  const isRtl = direction === "rtl";

  const cardBg = useColorModeValue("white", "gray.800");
  const statCardBg = useColorModeValue("wuilt.teal50", "gray.700");

  const stats = [
    { label: t("dashboard.communities"), value: communities.length, icon: Users },
    { label: t("dashboard.courses"), value: 0, icon: BookOpen }, // Placeholder
    { label: t("dashboard.messages"), value: 0, icon: MessageSquare }, // Placeholder
    { label: t("dashboard.engagement"), value: "0%", icon: TrendingUp }, // Placeholder
  ];

  return (
    <Box dir={direction}>
      <Box mb={{base: 6, md: 8}}>
        <Heading as="h1" size={{base: "xl", md: "2xl"}} mb={2}>
          {isRtl ? `مرحباً، ${userName}` : `Welcome, ${userName}`}
        </Heading>
        <Text color="gray.600" fontSize={{base: "md", md: "lg"}}>{t("dashboard.overviewDescription")}</Text>
      </Box>

      <SimpleGrid columns={{ base: 1, sm: 2, lg: 4 }} spacing={{base: 4, md: 6}} mb={{base: 6, md: 8}}>
        {stats.map((stat, index) => (
          <Card key={index} bg={statCardBg} shadow="sm">
            <CardBody>
              <Flex justify="space-between" align="center">
                <Stat>
                  <StatLabel fontSize="sm" color="gray.600">{stat.label}</StatLabel>
                  <StatNumber fontSize={{base: "2xl", md: "3xl"}} fontWeight="bold">{stat.value}</StatNumber>
                  {/* Placeholder for StatHelpText if dynamic data becomes available */}
                </Stat>
                <Box p={3} bg={cardBg} borderRadius="full" display="flex" alignItems="center" justifyContent="center" boxShadow="sm">
                  <Icon as={stat.icon} boxSize={{base: 4, md: 5}} color="wuilt.product" />
                </Box>
              </Flex>
            </CardBody>
          </Card>
        ))}
      </SimpleGrid>

      <Tabs colorScheme="teal" mb={{base: 6, md: 8}} variant="soft-rounded">
        <TabList overflowX="auto" overflowY="hidden">
          <Tab>{t("dashboard.overview")}</Tab>
          <Tab>{t("dashboard.communities")}</Tab>
          <Tab>{t("dashboard.courses")}</Tab>
          <Tab>{t("dashboard.content")}</Tab>
        </TabList>

        <TabPanels>
          <TabPanel px={0} py={{base: 4, md: 6}}>
            <SimpleGrid columns={{ base: 1, lg: 3 }} spacing={{base: 4, md: 6}}>
              <Card gridColumn={{ lg: "span 2" }} shadow="md">
                <CardHeader>
                  <Flex justify="space-between" align="center">
                    <Heading size="md">{t("dashboard.yourCommunities")}</Heading>
                    <Button variant="ghost" rightIcon={<ChevronRight size={16} />} size="sm" colorScheme="teal">
                      {t("dashboard.viewAll")}
                    </Button>
                  </Flex>
                </CardHeader>
                <CardBody>
                  {communities.length > 0 ? (
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={{base: 4, md: 6}}>
                      {communities.slice(0, 2).map((community) => (
                        <CommunityCard
                          key={community.id}
                          id={community.id}
                          name={community.name}
                          description={community.description ?? undefined}
                          thumbnailUrl={community.banner_url ?? undefined}
                          isPrivate={community.is_private ?? undefined}
                          language={language as "en" | "ar"}
                          slug={community.slug}
                        />
                      ))}
                    </SimpleGrid>
                  ) : (
                    <Flex direction="column" align="center" justify="center" py={{base: 8, md: 10}} borderWidth="1px" borderColor="gray.200" borderRadius="md" bg="gray.50" _dark={{ bg: "gray.700", borderColor: "gray.600" }}>
                      <Text mb={4} fontSize="lg">{t("dashboard.noCommunities")}</Text>
                      <Button leftIcon={<Plus size={16} />} colorScheme="teal">
                        {t("community.create")}
                      </Button>
                    </Flex>
                  )}
                </CardBody>
                {communities.length > 0 && (
                  <CardFooter borderTopWidth="1px" borderColor="gray.200" _dark={{borderColor:"gray.700"}}>
                    <Button leftIcon={<Plus size={16} />} w="full" variant="outline" colorScheme="teal">
                      {t("community.create")}
                    </Button>
                  </CardFooter>
                )}
              </Card>
              <Card shadow="md">
                <CardHeader><Heading size="md">{t("dashboard.recentActivity")}</Heading></CardHeader>
                <CardBody><Text color="gray.500">{t("dashboard.activityComingSoon", "Recent activity feed coming soon.")}</Text></CardBody>
              </Card>
            </SimpleGrid>
            <Box mt={{base: 6, md: 8}}>
              <Flex justify="space-between" align="center" mb={4}>
                <Heading size="md">{t("dashboard.yourCourses")}</Heading>
                <Button variant="ghost" rightIcon={<ChevronRight size={16} />} size="sm" colorScheme="teal">{t("dashboard.viewAll")}</Button>
              </Flex>
              <Text color="gray.500" gridColumn="1 / -1">{t("dashboard.coursesComingSoon", "Courses section coming soon.")}</Text>
            </Box>
          </TabPanel>

          <TabPanel px={0} py={{base: 4, md: 6}}>
            <Flex justify="space-between" align="center" mb={{base: 4, md: 6}}>
              <Heading size="md">{t("dashboard.allCommunities")}</Heading>
              <Button leftIcon={<Plus size={16} />} colorScheme="teal">{t("community.create")}</Button>
            </Flex>
            {communities.length > 0 ? (
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={{base: 4, md: 6}}>
                {communities.map((community) => (
                  <CommunityCard
                    key={community.id}
                    id={community.id}
                    name={community.name}
                    description={community.description ?? undefined}
                    thumbnailUrl={community.banner_url ?? undefined}
                    isPrivate={community.is_private ?? undefined}
                    language={language as "en" | "ar"}
                    slug={community.slug}
                  />
                ))}
              </SimpleGrid>
            ) : (
               <Flex direction="column" align="center" justify="center" py={{base: 8, md: 10}} borderWidth="1px" borderColor="gray.200" borderRadius="md" bg="gray.50" _dark={{ bg: "gray.700", borderColor: "gray.600" }} gridColumn="1 / -1">
                  <Text mb={4} fontSize="lg">{t("dashboard.noCommunities")}</Text>
                  <Button leftIcon={<Plus size={16} />} colorScheme="teal">{t("community.create")}</Button>
                </Flex>
            )}
          </TabPanel>

          <TabPanel px={0} py={{base: 4, md: 6}}>
            <Flex justify="space-between" align="center" mb={{base: 4, md: 6}}>
              <Heading size="md">{t("dashboard.allCourses")}</Heading>
              <Button leftIcon={<Plus size={16} />} colorScheme="teal">{t("course.create")}</Button>
            </Flex>
             <Text color="gray.500">{t("dashboard.coursesComingSoon", "Full courses list coming soon.")}</Text>
          </TabPanel>

          <TabPanel px={0} py={{base: 4, md: 6}}>
            <Heading size="md" mb={{base: 4, md: 6}}>{t("dashboard.yourContent")}</Heading>
            <Text color="gray.500">{t("dashboard.contentFeedComingSoon", "Content feed coming soon.")}</Text>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default DashboardOverview;
