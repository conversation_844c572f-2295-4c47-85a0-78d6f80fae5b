"use client";

import React from "react";
import {
  Box,
  Flex,
  Heading,
  Text,
  Input,
  InputGroup,
  InputLeftElement,
  SimpleGrid,
  Card,
  CardBody,
  Icon,
  Link,
  Avatar,
  HStack,
  Badge,
  Button,
  Divider,
  useColorModeValue,
} from "@chakra-ui/react";
import {
  Search,
  LifeBuoy,
  MessageSquare,
  FileText,
  Zap,
  Ticket,
  User,
} from "lucide-react";
import NextLink from "next/link";

export default function CustomerSelfServicePortal() {
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const iconBgColor = useColorModeValue("blue.50", "blue.900");
  const cardBgColor = useColorModeValue("white", "gray.800");

  return (
    <Box>
      {/* Header */}
      <Flex
        as="header"
        align="center"
        justify="space-between"
        py={4}
        px={8}
        borderBottom="1px"
        borderColor={borderColor}
        bg={bgColor}
      >
        <Flex align="center">
          <Box
            as="span"
            fontSize="2xl"
            fontWeight="bold"
            color="blue.500"
            mr={2}
          >
            Bytelink
          </Box>
          <HStack spacing={6} ml={10} display={{ base: "none", md: "flex" }}>
            <Link fontWeight="medium">Home</Link>
            <Link fontWeight="medium">Company</Link>
            <Link fontWeight="medium">Resources</Link>
            <Link fontWeight="medium">Help center</Link>
            <Link fontWeight="medium">Support</Link>
          </HStack>
        </Flex>
        <Flex align="center">
          <InputGroup
            size="sm"
            maxW="200px"
            mr={4}
            display={{ base: "none", md: "block" }}
          >
            <InputLeftElement pointerEvents="none">
              <Search size={16} color="gray.400" />
            </InputLeftElement>
            <Input placeholder="Search..." rounded="full" />
          </InputGroup>
          <Button size="sm" colorScheme="blue" variant="outline">
            {t("auth.signIn", "Log in")}
          </Button>
          <LanguageSwitcher />
        </Flex>
      </Flex>

      {/* Hero Section */}
      <Box bg="blue.50" py={12} px={8}>
        <Flex
          direction={{ base: "column", md: "row" }}
          maxW="container.xl"
          mx="auto"
          align="center"
          justify="space-between"
        >
          <Box maxW={{ base: "100%", md: "50%" }} mb={{ base: 8, md: 0 }}>
            <Heading as="h1" size="xl" mb={4}>
              Home
            </Heading>
            <Text fontSize="lg" color="gray.600" mb={6}>
              Welcome to the Bytelink Community Hub. In here you will find
              guides, discussion forums, tools, and much more — Enjoy!
            </Text>
            <InputGroup size="lg" maxW="500px">
              <InputLeftElement pointerEvents="none">
                <Search size={20} color="gray.400" />
              </InputLeftElement>
              <Input
                placeholder="Search..."
                bg="white"
                rounded="md"
                borderColor={borderColor}
                _focus={{
                  borderColor: "blue.400",
                  boxShadow: "0 0 0 1px var(--chakra-colors-blue-400)",
                }}
              />
            </InputGroup>
          </Box>
          <Box maxW={{ base: "100%", md: "45%" }}>
            <img
              src="https://images.unsplash.com/photo-1573164713988-8665fc963095?w=600&q=80"
              alt="Community Support"
              style={{ borderRadius: "8px", maxWidth: "100%" }}
            />
          </Box>
        </Flex>
      </Box>

      {/* Main Categories */}
      <Box py={12} px={8}>
        <SimpleGrid
          columns={{ base: 1, md: 3 }}
          spacing={8}
          maxW="container.xl"
          mx="auto"
        >
          {/* Help Center */}
          <Card borderColor={borderColor} shadow="sm">
            <CardBody>
              <Flex mb={4}>
                <Flex
                  align="center"
                  justify="center"
                  w="50px"
                  h="50px"
                  bg="green.100"
                  color="green.500"
                  borderRadius="md"
                >
                  <Icon as={LifeBuoy} boxSize={6} />
                </Flex>
              </Flex>
              <Heading as="h3" size="md" mb={2}>
                Help center
              </Heading>
              <Text color="gray.600" mb={4}>
                Have some help? We've got answers. Browse our expansive help
                center to learn how to fix common issues and problems.
              </Text>
              <Link color="blue.500" fontWeight="medium">
                Browse help center →
              </Link>
            </CardBody>
          </Card>

          {/* Support */}
          <Card borderColor={borderColor} shadow="sm">
            <CardBody>
              <Flex mb={4}>
                <Flex
                  align="center"
                  justify="center"
                  w="50px"
                  h="50px"
                  bg="purple.100"
                  color="purple.500"
                  borderRadius="md"
                >
                  <Icon as={MessageSquare} boxSize={6} />
                </Flex>
              </Flex>
              <Heading as="h3" size="md" mb={2}>
                Support
              </Heading>
              <Text color="gray.600" mb={4}>
                Need some help or assistance with anything related to our
                services? Get an answer from our dedicated experts and community
                members.
              </Text>
              <Link color="blue.500" fontWeight="medium">
                Get support →
              </Link>
            </CardBody>
          </Card>

          {/* Articles */}
          <Card borderColor={borderColor} shadow="sm">
            <CardBody>
              <Flex mb={4}>
                <Flex
                  align="center"
                  justify="center"
                  w="50px"
                  h="50px"
                  bg="orange.100"
                  color="orange.500"
                  borderRadius="md"
                >
                  <Icon as={FileText} boxSize={6} />
                </Flex>
              </Flex>
              <Heading as="h3" size="md" mb={2}>
                Articles
              </Heading>
              <Text color="gray.600" mb={4}>
                Seeking knowledge and solutions? Dive into our articles for
                comprehensive information and practical guidance on various
                topics.
              </Text>
              <Link color="blue.500" fontWeight="medium">
                Browse all articles →
              </Link>
            </CardBody>
          </Card>

          {/* Company Updates */}
          <Card borderColor={borderColor} shadow="sm">
            <CardBody>
              <Flex mb={4}>
                <Flex
                  align="center"
                  justify="center"
                  w="50px"
                  h="50px"
                  bg="blue.100"
                  color="blue.500"
                  borderRadius="md"
                >
                  <Icon as={Zap} boxSize={6} />
                </Flex>
              </Flex>
              <Heading as="h3" size="md" mb={2}>
                Company updates
              </Heading>
              <Text color="gray.600" mb={4}>
                Stay informed about latest developments. Access the most recent
                news, announcements, and insights about our organization.
              </Text>
              <Link color="blue.500" fontWeight="medium">
                View updates →
              </Link>
            </CardBody>
          </Card>

          {/* Offers and Promos */}
          <Card borderColor={borderColor} shadow="sm">
            <CardBody>
              <Flex mb={4}>
                <Flex
                  align="center"
                  justify="center"
                  w="50px"
                  h="50px"
                  bg="yellow.100"
                  color="yellow.500"
                  borderRadius="md"
                >
                  <Icon as={Ticket} boxSize={6} />
                </Flex>
              </Flex>
              <Heading as="h3" size="md" mb={2}>
                Offers and promos
              </Heading>
              <Text color="gray.600" mb={4}>
                Don't miss out on exclusive deals! Discover the latest discounts
                and promotions tailored to enhance your experience and save you
                money.
              </Text>
              <Link color="blue.500" fontWeight="medium">
                View offers →
              </Link>
            </CardBody>
          </Card>

          {/* Use Cases */}
          <Card borderColor={borderColor} shadow="sm">
            <CardBody>
              <Flex mb={4}>
                <Flex
                  align="center"
                  justify="center"
                  w="50px"
                  h="50px"
                  bg="red.100"
                  color="red.500"
                  borderRadius="md"
                >
                  <Icon as={User} boxSize={6} />
                </Flex>
              </Flex>
              <Heading as="h3" size="md" mb={2}>
                Use cases
              </Heading>
              <Text color="gray.600" mb={4}>
                Explore how customers thrive using our internet services in this
                section—discover real-world applications and success stories.
              </Text>
              <Link color="blue.500" fontWeight="medium">
                View use cases →
              </Link>
            </CardBody>
          </Card>
        </SimpleGrid>
      </Box>

      {/* Featured Discussions */}
      <Box py={8} px={8} bg="gray.50">
        <Box maxW="container.xl" mx="auto">
          <Flex justify="space-between" align="center" mb={6}>
            <Heading as="h2" size="lg">
              Featured discussions
            </Heading>
            <Link color="blue.500" fontWeight="medium">
              Browse all discussions
            </Link>
          </Flex>

          <SimpleGrid columns={{ base: 1, md: 1 }} spacing={4}>
            {[
              {
                id: 1,
                title: "Should I use dual-band or single-band?",
                subtitle:
                  "What are the pros and cons of Dual-Band vs. Single-Band Routers?",
                author: "Alex Johnson",
                avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Alex",
                time: "a year ago",
                comments: 3,
                likes: 1,
              },
              {
                id: 2,
                title: "Dealing with upload speed problems",
                subtitle:
                  "My upload speed is really low. How can I improve it for smoother video calls?",
                author: "Samantha Lee",
                avatar:
                  "https://api.dicebear.com/7.x/avataaars/svg?seed=Samantha",
                time: "a year ago",
                comments: 7,
                likes: 3,
              },
              {
                id: 3,
                title: "How to extend WiFi range?",
                subtitle:
                  "My WiFi doesn't reach certain rooms. How can I extend the range?",
                author: "Michael Chen",
                avatar:
                  "https://api.dicebear.com/7.x/avataaars/svg?seed=Michael",
                time: "a year ago",
                comments: 5,
                likes: 2,
              },
              {
                id: 4,
                title: "Is anyone else experiencing downtime?",
                subtitle:
                  "My internet is down. Is it just me or are others facing this issue too?",
                author: "Emma Wilson",
                avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Emma",
                time: "a year ago",
                comments: 8,
                likes: 1,
              },
              {
                id: 5,
                title: "How to set guest networks?",
                subtitle:
                  "Want to create a separate network for guests? How can I do this on my router?",
                author: "David Kim",
                avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=David",
                time: "a year ago",
                comments: 4,
                likes: 0,
              },
            ].map((discussion) => (
              <Card key={discussion.id} borderColor={borderColor} shadow="sm">
                <CardBody>
                  <Flex justify="space-between">
                    <Flex align="center">
                      <Avatar size="sm" src={discussion.avatar} mr={3} />
                      <Box>
                        <Link as={NextLink} href="#" fontWeight="medium">
                          {discussion.title}
                        </Link>
                        <Text fontSize="sm" color="gray.600">
                          {discussion.subtitle}
                        </Text>
                        <HStack spacing={4} mt={1}>
                          <Text fontSize="xs" color="gray.500">
                            {discussion.time}
                          </Text>
                          <HStack spacing={1}>
                            <Text fontSize="xs" color="gray.500">
                              {discussion.comments}
                            </Text>
                            <Text fontSize="xs" color="gray.500">
                              comments
                            </Text>
                          </HStack>
                          <HStack spacing={1}>
                            <Text fontSize="xs" color="gray.500">
                              {discussion.likes}
                            </Text>
                            <Text fontSize="xs" color="gray.500">
                              likes
                            </Text>
                          </HStack>
                        </HStack>
                      </Box>
                    </Flex>
                  </Flex>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>

          <Box textAlign="center" mt={6}>
            <Button variant="outline" colorScheme="blue">
              Show more
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Recent Updates */}
      <Box py={12} px={8}>
        <Box maxW="container.xl" mx="auto">
          <Flex
            direction={{ base: "column", lg: "row" }}
            justify="space-between"
            align="start"
            gap={8}
          >
            <Box flex="2">
              <Flex justify="space-between" align="center" mb={6}>
                <Heading as="h2" size="lg">
                  Featured articles
                </Heading>
                <Link color="blue.500" fontWeight="medium">
                  Browse all articles
                </Link>
              </Flex>

              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                <Card borderColor={borderColor} shadow="sm" overflow="hidden">
                  <Box height="200px" overflow="hidden">
                    <img
                      src="https://images.unsplash.com/photo-1558346490-a72e53ae2d4f?w=500&q=80"
                      alt="WiFi security"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  </Box>
                  <CardBody>
                    <Heading as="h3" size="md" mb={2}>
                      How to secure your home Wi-Fi network
                    </Heading>
                    <HStack spacing={2} mb={3}>
                      <Badge colorPalette="blue">Security</Badge>
                      <Badge colorPalette="gray">Networking</Badge>
                    </HStack>
                    <Text noOfLines={3} color="gray.600">
                      Learn the essential steps to protect your home network
                      from unauthorized access and potential security threats.
                    </Text>
                  </CardBody>
                </Card>

                <Card borderColor={borderColor} shadow="sm" overflow="hidden">
                  <Box height="200px" overflow="hidden">
                    <img
                      src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?w=500&q=80"
                      alt="Smart home"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  </Box>
                  <CardBody>
                    <Heading as="h3" size="md" mb={2}>
                      Building a smart home with IoT devices
                    </Heading>
                    <HStack spacing={2} mb={3}>
                      <Badge colorPalette="green">Smart Home</Badge>
                      <Badge colorPalette="purple">IoT</Badge>
                    </HStack>
                    <Text noOfLines={3} color="gray.600">
                      Discover how to create an interconnected smart home
                      ecosystem with various IoT devices for convenience and
                      efficiency.
                    </Text>
                  </CardBody>
                </Card>
              </SimpleGrid>
            </Box>

            <Box flex="1">
              <Flex justify="space-between" align="center" mb={6}>
                <Heading as="h2" size="lg">
                  Recent Updates
                </Heading>
              </Flex>

              <Card borderColor={borderColor} shadow="sm" mb={6}>
                <CardBody>
                  <Badge colorScheme="blue" mb={2}>
                    Network
                  </Badge>
                  <Heading as="h3" size="md" mb={2}>
                    Welcome the speed of tomorrow with 5G
                  </Heading>
                  <Text color="gray.600" mb={4} noOfLines={3}>
                    Experience lightning-fast connectivity with our expanded 5G
                    network coverage. Now available in more areas!
                  </Text>
                  <HStack spacing={4}>
                    <Text fontSize="sm" color="gray.500">
                      2 days ago
                    </Text>
                  </HStack>
                </CardBody>
              </Card>

              <Card borderColor={borderColor} shadow="sm">
                <CardBody>
                  <Badge colorPalette="green" mb={2}>
                    Pricing
                  </Badge>
                  <Heading as="h3" size="md" mb={2}>
                    Lowering costs for existing customers
                  </Heading>
                  <Text color="gray.600" mb={4} noOfLines={3}>
                    We're reducing monthly fees for all existing customers on
                    premium plans. Check your next bill for the new rates!
                  </Text>
                  <HStack spacing={4}>
                    <Text fontSize="sm" color="gray.500">
                      1 week ago
                    </Text>
                  </HStack>
                </CardBody>
              </Card>
            </Box>
          </Flex>
        </Box>
      </Box>

      {/* Latest Help Center Posts */}
      <Box py={8} px={8} bg="gray.50">
        <Box maxW="container.xl" mx="auto">
          <Flex justify="space-between" align="center" mb={6}>
            <Heading as="h2" size="lg">
              Latest help center posts
            </Heading>
            <Link color="blue.500" fontWeight="medium">
              Browse all posts
            </Link>
          </Flex>

          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
            {[
              {
                id: 1,
                title: "How to set up parental controls",
                category: "Internet safety",
                time: "10 minutes ago",
                excerpt:
                  "Hello, Bytelink users! Keeping children safe while they explore the digital world is essential. In this guide, we'll show you how to set up parent...",
                tags: ["Parental Controls"],
              },
              {
                id: 2,
                title: "How to reset your router to factory settings",
                category: "Router configuration",
                time: "30 minutes ago",
                excerpt:
                  "Hello, Bytelink users! Sometimes resetting your router to factory settings is necessary, whether you want to start fresh or troubleshoot issues. In this...",
                tags: ["Router Configuration"],
              },
              {
                id: 3,
                title: "Setting up port forwarding on your router",
                category: "Router configuration",
                time: "1 hour ago",
                excerpt:
                  "Hello, Bytelink users! Port forwarding is a crucial configuration that allows external devices to connect to specific services or applications...",
                tags: ["Security and privacy"],
              },
            ].map((post) => (
              <Card key={post.id} borderColor={borderColor} shadow="sm">
                <CardBody>
                  <Badge
                    colorScheme="orange"
                    mb={2}
                    fontSize="xs"
                    textTransform="uppercase"
                  >
                    {post.category}
                  </Badge>
                  <Heading as="h3" size="md" mb={2}>
                    {post.title}
                  </Heading>
                  <Text fontSize="sm" color="gray.500" mb={3}>
                    {post.time}
                  </Text>
                  <Text noOfLines={3} mb={4} color="gray.600">
                    {post.excerpt}
                  </Text>
                  <Badge colorScheme="yellow" fontSize="xs">
                    {post.tags[0]}
                  </Badge>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>

          <Box textAlign="center" mt={6}>
            <Button variant="outline" colorScheme="blue">
              Show more
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Footer */}
      <Box as="footer" bg="gray.800" color="white" py={10} px={8}>
        <Box maxW="container.xl" mx="auto">
          <SimpleGrid columns={{ base: 1, md: 4 }} spacing={8}>
            <Box>
              <Heading size="md" mb={4}>
                Bytelink
              </Heading>
              <Text color="gray.400" mb={4}>
                Providing high-speed internet solutions for homes and
                businesses.
              </Text>
            </Box>
            <Box>
              <Heading size="sm" mb={4}>
                Company
              </Heading>
              <Flex direction="column" gap={2}>
                <Link>About Us</Link>
                <Link>Careers</Link>
                <Link>Press</Link>
                <Link>Contact</Link>
              </Flex>
            </Box>
            <Box>
              <Heading size="sm" mb={4}>
                Resources
              </Heading>
              <Flex direction="column" gap={2}>
                <Link>Blog</Link>
                <Link>Help Center</Link>
                <Link>Community</Link>
                <Link>Developers</Link>
              </Flex>
            </Box>
            <Box>
              <Heading size="sm" mb={4}>
                Legal
              </Heading>
              <Flex direction="column" gap={2}>
                <Link>Terms of Service</Link>
                <Link>Privacy Policy</Link>
                <Link>Cookie Policy</Link>
                <Link>GDPR</Link>
              </Flex>
            </Box>
          </SimpleGrid>
          <Divider my={6} borderColor="gray.700" />
          <Text color="gray.400" fontSize="sm">
            © 2023 Bytelink. All rights reserved.
          </Text>
        </Box>
      </Box>
    </Box>
  );
}
