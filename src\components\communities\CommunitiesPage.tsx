"use client";

import { useState } from "react";
import {
  Box,
  Button,
  Heading,
  Text,
  SimpleGrid,
  Flex,
  Card,
  CardBody,
  useDisclosure,
} from "@chakra-ui/react";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON>b<PERSON><PERSON>, <PERSON>b, <PERSON>b<PERSON><PERSON><PERSON>, TabPanel } from "@/components/ui/tabs";
import { useTranslation } from "react-i18next";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Search, Plus } from "lucide-react";
import CommunityCard from "@/components/community/CommunityCard";
import CreateCommunityModal from "./CreateCommunityModal";

interface CommunitiesPageProps {
  userCommunities: any[];
  publicCommunities: any[];
  language: string;
}

export default function CommunitiesPage({
  userCommunities = [],
  publicCommunities = [],
  language = "en",
}: CommunitiesPageProps) {
  const { t } = useTranslation();
  const direction = "ltr"; // Temporarily hardcode to isolate issue
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [searchQuery, setSearchQuery] = useState("");

  const filterCommunities = (communities: any[]) => {
    if (!searchQuery) return communities;

    return communities.filter(
      (community) =>
        community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (community.description &&
          community.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase())),
    );
  };

  const filteredUserCommunities = filterCommunities(userCommunities);
  const filteredPublicCommunities = filterCommunities(publicCommunities);

  return (
    <Box dir={direction}>
      <Flex justify="space-between" align="center" mb={6}>
        <Box>
          <Heading as="h1" size="xl" mb={2}>
            {t("communities.title")}
          </Heading>
          <Text color="gray.600">{t("communities.description")}</Text>
        </Box>
        <Button leftIcon={<Plus size={16} />} onClick={onOpen}>
          {t("community.create")}
        </Button>
      </Flex>

      <Box mb={6}>
        <Input
          placeholder={t("communities.searchPlaceholder")}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </Box>

      <Tabs colorPalette="teal">
        <TabList mb={6}>
          <Tab>{t("communities.myCommunities")}</Tab>
          <Tab>{t("communities.discover")}</Tab>
        </TabList>

        <TabPanels>
          {/* My Communities Tab */}
          <TabPanel px={0}>
            {filteredUserCommunities.length > 0 ? (
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                {filteredUserCommunities.map((community, index) => (
                  <CommunityCard
                    key={community.id || index}
                    id={community.id}
                    name={community.name}
                    description={community.description}
                    thumbnailUrl={community.banner_url}
                    isPrivate={community.is_private}
                    language={language as "en" | "ar"}
                  />
                ))}
              </SimpleGrid>
            ) : (
              <Card p={10} textAlign="center">
                <CardBody>
                  <Text mb={4}>
                    {searchQuery
                      ? t("communities.noSearchResults")
                      : t("communities.noCommunities")}
                  </Text>
                  <Button leftIcon={<Plus size={16} />} onClick={onOpen}>
                    {t("community.create")}
                  </Button>
                </CardBody>
              </Card>
            )}
          </TabPanel>

          {/* Discover Tab */}
          <TabPanel px={0}>
            {filteredPublicCommunities.length > 0 ? (
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                {filteredPublicCommunities.map((community, index) => (
                  <CommunityCard
                    key={community.id || index}
                    id={community.id}
                    name={community.name}
                    description={community.description}
                    thumbnailUrl={community.banner_url}
                    isPrivate={community.is_private}
                    language={language as "en" | "ar"}
                  />
                ))}
              </SimpleGrid>
            ) : (
              <Card p={10} textAlign="center">
                <CardBody>
                  <Text>
                    {searchQuery
                      ? t("communities.noSearchResults")
                      : t("communities.noPublicCommunities")}
                  </Text>
                </CardBody>
              </Card>
            )}
          </TabPanel>
        </TabPanels>
      </Tabs>

      <CreateCommunityModal isOpen={isOpen} onClose={onClose} />
    </Box>
  );
}
