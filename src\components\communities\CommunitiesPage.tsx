"use client";

import { useState } from "react";
import {
  Box,
  Button,
  Heading,
  Text,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";

interface CommunitiesPageProps {
  userCommunities: any[];
  publicCommunities: any[];
  language: string;
}

export default function CommunitiesPage({
  userCommunities = [],
  publicCommunities = [],
  language = "en",
}: CommunitiesPageProps) {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <Box>
      <Heading as="h1" size="xl" mb={4}>
        {t("communities.title")}
      </Heading>
      <Text mb={4}>
        {t("communities.description")}
      </Text>
      <Button onClick={() => setIsModalOpen(true)}>
        {t("community.create")}
      </Button>

      <Text mt={4}>
        User Communities: {userCommunities.length}
      </Text>
      <Text>
        Public Communities: {publicCommunities.length}
      </Text>
    </Box>
  );
}
