"use client";

import { useState } from "react";
import {
  Box,
  Button,
  Heading,
  Text,
} from "@chakra-ui/react";

interface CommunitiesPageProps {
  userCommunities: any[];
  publicCommunities: any[];
  language: string;
}

export default function CommunitiesPage({
  userCommunities = [],
  publicCommunities = [],
  language = "en",
}: CommunitiesPageProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <Box>
      <Heading as="h1" size="xl" mb={4}>
        Communities
      </Heading>
      <Text mb={4}>
        Discover and join communities
      </Text>
      <Button onClick={() => setIsModalOpen(true)}>
        Create Community
      </Button>

      <Text mt={4}>
        User Communities: {userCommunities.length}
      </Text>
      <Text>
        Public Communities: {publicCommunities.length}
      </Text>
    </Box>
  );
}
