"use client";

import { useEffect } from "react";
import { DirectionProvider } from "@/lib/contexts/DirectionContext";
import { Provider } from "@/components/ui/provider";
import "@/lib/i18n";

export function Providers({ children }: { children: React.ReactNode }) {
  // Initialize i18next on the client side
  useEffect(() => {
    // This is intentionally left empty as i18next is initialized in the import
  }, []);

  return (
    <Provider>
      <DirectionProvider>{children}</DirectionProvider>
    </Provider>
  );
}
