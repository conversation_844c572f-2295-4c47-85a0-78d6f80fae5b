"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Input,
  Stack,
  Text,
  Link,
  Heading,
  Flex,
  Card,
  CardBody,
} from "@chakra-ui/react";
import { FormControl, FormLabel, FormErrorMessage } from "@/components/ui/form";
import { toaster } from "@/components/ui/toaster";
import { supabase } from "@/lib/supabase/client";
import { useDirection } from "@/lib/contexts/DirectionContext";
import LanguageSwitcher from "./LanguageSwitcher";

type AuthMode = "signIn" | "signUp";

interface AuthFormProps {
  mode?: AuthMode;
}

export default function AuthForm({
  mode: initialMode = "signIn",
}: AuthFormProps) {
  const { t } = useTranslation();
  const { direction } = useDirection();
  const router = useRouter();

  const [mode, setMode] = useState<AuthMode>(initialMode);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [fullName, setFullName] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const toggleMode = () => {
    setMode(mode === "signIn" ? "signUp" : "signIn");
    setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (mode === "signUp") {
        const { data, error: signUpError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: fullName,
            },
          },
        });

        if (signUpError) throw signUpError;

        if (data.user) {
          // Create a profile record
          const { error: profileError } = await supabase
            .from("profiles")
            .insert([
              {
                id: data.user.id,
                username: email.split("@")[0],
                full_name: fullName,
                avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${data.user.id}`,
                language: direction === "rtl" ? "ar" : "en",
              },
            ]);

          if (profileError) throw profileError;

          toaster.success({
            title: "Account created successfully",
            description: "Please check your email to verify your account",
            duration: 5000,
          });

          // Auto-login after signup
          const { error: signInError } = await supabase.auth.signInWithPassword(
            {
              email,
              password,
            },
          );

          if (!signInError) {
            router.push("/dashboard");
            router.refresh();
          }
        }
      } else {
        const { error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (signInError) throw signInError;

        router.push("/dashboard");
        router.refresh();
      }
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card width="100%" maxW="450px" boxShadow="lg" dir={direction}>
      <CardBody p={8}>
        <Stack spacing={6}>
          <Heading as="h2" size="lg" textAlign="center">
            {mode === "signIn" ? t("auth.signIn") : t("auth.signUp")}
          </Heading>

          <Box mb={4} textAlign="center">
            <LanguageSwitcher />
          </Box>

          <form onSubmit={handleSubmit}>
            <Stack spacing={4}>
              {mode === "signUp" && (
                <FormControl id="fullName" isRequired>
                  <FormLabel>{t("auth.fullName")}</FormLabel>
                  <Input
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="John Doe"
                  />
                </FormControl>
              )}

              <FormControl id="email" isRequired>
                <FormLabel>{t("auth.email")}</FormLabel>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </FormControl>

              <FormControl id="password" isRequired isInvalid={!!error}>
                <FormLabel>{t("auth.password")}</FormLabel>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="********"
                />
                <FormErrorMessage>{error}</FormErrorMessage>
              </FormControl>

              <Button
                type="submit"
                size="lg"
                fontSize="md"
                isLoading={loading}
                w="100%"
                mt={4}
              >
                {mode === "signIn" ? t("auth.signIn") : t("auth.signUp")}
              </Button>
            </Stack>
          </form>

          <Flex justify="center">
            <Text>
              {mode === "signIn" ? t("auth.noAccount") : t("auth.haveAccount")}{" "}
              <Link color="wuilt.product" onClick={toggleMode} cursor="pointer">
                {mode === "signIn" ? t("auth.signUp") : t("auth.signIn")}
              </Link>
            </Text>
          </Flex>

          {mode === "signIn" && (
            <Link
              color="wuilt.product"
              href="/forgot-password"
              textAlign="center"
            >
              {t("auth.forgotPassword")}
            </Link>
          )}
        </Stack>
      </CardBody>
    </Card>
  );
}
