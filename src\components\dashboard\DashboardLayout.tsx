"use client";

import { ReactNode, useState } from "react";
import { useDirection } from "@/lib/contexts/DirectionContext";

interface DashboardLayoutProps {
  children: ReactNode;
  user: any;
}

export default function DashboardLayout({
  children,
  user,
}: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { direction } = useDirection();

  return (
    <div
      className="min-h-screen bg-gray-50"
      dir={direction}
    >
      {/* Sidebar for desktop */}
      <div className="hidden md:block fixed inset-y-0 left-0 w-60 bg-white border-r border-gray-200">
        <div className="flex h-20 items-center justify-center px-8">
          <h1 className="text-2xl font-bold text-teal-600">Scenius</h1>
        </div>
        <nav className="px-4 space-y-2">
          <a href="/dashboard" className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
            Dashboard
          </a>
          <a href="/communities" className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
            Communities
          </a>
          <a href="/spaces" className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
            Spaces
          </a>
          <a href="/settings" className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
            Settings
          </a>
        </nav>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setSidebarOpen(false)} />
          <div className="fixed inset-y-0 left-0 w-60 bg-white">
            <div className="flex h-20 items-center justify-between px-8">
              <h1 className="text-2xl font-bold text-teal-600">Scenius</h1>
              <button onClick={() => setSidebarOpen(false)} className="text-gray-500">
                ✕
              </button>
            </div>
            <nav className="px-4 space-y-2">
              <a href="/dashboard" className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
                Dashboard
              </a>
              <a href="/communities" className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
                Communities
              </a>
              <a href="/spaces" className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
                Spaces
              </a>
              <a href="/settings" className="flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
                Settings
              </a>
            </nav>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="md:ml-60">
        {/* Top navbar */}
        <div className="bg-white border-b border-gray-200 px-4 py-4 flex items-center justify-between">
          <button
            className="md:hidden text-gray-500"
            onClick={() => setSidebarOpen(true)}
          >
            ☰
          </button>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">Welcome, {user?.full_name || "User"}</span>
            <div className="w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white text-sm">
              {(user?.full_name || "U")[0]}
            </div>
          </div>
        </div>

        {/* Page content */}
        <div className="p-4">
          {children}
        </div>
      </div>
    </div>
  );
}
