"use client";

import React, { useState } from "react";
import {
  Box,
  Flex,
  Heading,
  Text,
  Input,
  InputGroup,
  InputLeftElement,
  Button,
  HStack,
  VStack,
  Avatar,
  Icon,
  Divider,
  Badge,
  Card,
  CardBody,
  Link,
  useColorModeValue,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Tabs,
  TabList,
  Tab,
  Select,
  Container,
  Image,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Textarea,
  SimpleGrid,
} from "@chakra-ui/react";
import {
  Search,
  Home,
  TrendingUp,
  Compass,
  List,
  MessageSquare,
  ArrowUp,
  ArrowDown,
  Share2,
  MoreHorizontal,
  ChevronUp,
  Bell,
  User,
  Plus,
  Image as ImageIcon,
  Link as LinkIcon,
  Award,
  Filter,
  Settings,
  LogOut,
  HelpCircle,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { useDirection } from "@/lib/contexts/DirectionContext";
import NextLink from "next/link";

export default function RedditLikeCommunity() {
  const { t } = useTranslation();
  const { direction } = useDirection();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [activeTab, setActiveTab] = useState("hot");

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const navBgColor = useColorModeValue("white", "gray.900");
  const cardBgColor = useColorModeValue("white", "gray.800");
  const hoverBgColor = useColorModeValue("gray.50", "gray.700");
  const textColor = useColorModeValue("gray.800", "gray.100");
  const secondaryTextColor = useColorModeValue("gray.600", "gray.400");

  return (
    <Box dir={direction}>
      {/* Header */}
      <Flex
        as="header"
        align="center"
        justify="space-between"
        py={3}
        px={8}
        borderBottom="1px"
        borderColor={borderColor}
        bg={navBgColor}
        position="sticky"
        top={0}
        zIndex={10}
      >
        <Flex align="center">
          <Heading
            as="h1"
            fontSize="xl"
            fontWeight="bold"
            color="orange.500"
            mr={8}
          >
            {t("redditLike.title", "reddify")}
          </Heading>
          <HStack spacing={4} display={{ base: "none", md: "flex" }}>
            <Button variant="ghost" size="sm" px={3}>
              {t("redditLike.best", "Best")}
            </Button>
            <Button variant="ghost" size="sm" px={3}>
              {t("redditLike.new", "New")}
            </Button>
            <Button variant="ghost" size="sm" px={3}>
              {t("redditLike.hot", "Hot")}
            </Button>
            <Button variant="ghost" size="sm" px={3}>
              {t("redditLike.top", "Top")}
            </Button>
          </HStack>
        </Flex>

        <Flex align="center" flex={1} mx={4} maxW="600px">
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <Search size={18} color="gray.400" />
            </InputLeftElement>
            <Input
              placeholder={t(
                "redditLike.searchPlaceholder",
                "Search or ask a question (Ctrl + /)",
              )}
              bg="gray.100"
              _dark={{ bg: "gray.700" }}
              border="none"
              borderRadius="full"
            />
          </InputGroup>
        </Flex>

        <HStack spacing={3}>
          <LanguageSwitcher />
          <Button variant="outline" size="sm">
            {t("redditLike.login", "Log in")}
          </Button>
          <Button
            colorScheme="orange"
            size="sm"
            display={{ base: "none", md: "flex" }}
          >
            {t("redditLike.signup", "Sign up")}
          </Button>
          <Menu>
            <MenuButton
              as={IconButton}
              aria-label="Options"
              icon={<User size={18} />}
              variant="ghost"
              size="sm"
            />
            <MenuList>
              <MenuItem icon={<User size={16} />}>
                {t("redditLike.profile", "Profile")}
              </MenuItem>
              <MenuItem icon={<Settings size={16} />}>
                {t("redditLike.settings", "Settings")}
              </MenuItem>
              <MenuItem icon={<HelpCircle size={16} />}>
                {t("redditLike.help", "Help")}
              </MenuItem>
              <MenuItem icon={<LogOut size={16} />}>
                {t("redditLike.logout", "Log out")}
              </MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </Flex>

      <Flex>
        {/* Sidebar */}
        <Box
          w="240px"
          h="calc(100vh - 56px)"
          borderRight="1px"
          borderColor={borderColor}
          p={4}
          position="sticky"
          top="56px"
          display={{ base: "none", lg: "block" }}
          overflowY="auto"
        >
          <VStack align="stretch" spacing={1} mb={6}>
            <Button
              variant="ghost"
              justifyContent="flex-start"
              leftIcon={<Icon as={Home} boxSize={5} />}
              size="sm"
              borderRadius="md"
              fontWeight="medium"
            >
              {t("redditLike.home", "Home")}
            </Button>
            <Button
              variant="ghost"
              justifyContent="flex-start"
              leftIcon={<Icon as={TrendingUp} boxSize={5} />}
              size="sm"
              borderRadius="md"
              fontWeight="medium"
            >
              {t("redditLike.popular", "Popular")}
            </Button>
            <Button
              variant="ghost"
              justifyContent="flex-start"
              leftIcon={<Icon as={Compass} boxSize={5} />}
              size="sm"
              borderRadius="md"
              fontWeight="medium"
            >
              {t("redditLike.explore", "Explore")}
            </Button>
            <Button
              variant="ghost"
              justifyContent="flex-start"
              leftIcon={<Icon as={List} boxSize={5} />}
              size="sm"
              borderRadius="md"
              fontWeight="medium"
            >
              {t("redditLike.all", "All")}
            </Button>
          </VStack>

          <Box mb={6}>
            <Text
              fontSize="xs"
              fontWeight="bold"
              textTransform="uppercase"
              color="gray.500"
              mb={2}
              px={3}
            >
              {t("redditLike.communities", "Communities")}
            </Text>
            <VStack align="stretch" spacing={1}>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="Announcements"
                    bg="red.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/announcements
              </Button>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="Today I Learned"
                    bg="blue.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/todayilearned
              </Button>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="Ask Reddify"
                    bg="purple.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/askReddify
              </Button>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="Tech Support"
                    bg="green.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/techsupport
              </Button>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="World News"
                    bg="orange.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/worldnews
              </Button>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="Books"
                    bg="yellow.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/books
              </Button>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="Art"
                    bg="pink.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/art
              </Button>
            </VStack>
          </Box>

          <Box mb={6}>
            <Text
              fontSize="xs"
              fontWeight="bold"
              textTransform="uppercase"
              color="gray.500"
              mb={2}
              px={3}
            >
              {t("redditLike.moderating", "Moderating")}
            </Text>
            <VStack align="stretch" spacing={1}>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="Programming"
                    bg="cyan.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/programming
              </Button>
              <Button
                variant="ghost"
                justifyContent="flex-start"
                leftIcon={
                  <Avatar
                    size="xs"
                    name="Web Dev"
                    bg="teal.500"
                    color="white"
                    fontSize="xs"
                  />
                }
                size="sm"
                borderRadius="md"
                fontWeight="medium"
              >
                r/webdev
              </Button>
            </VStack>
          </Box>

          <Divider my={4} />

          <VStack align="stretch" spacing={1}>
            <Button
              variant="ghost"
              justifyContent="flex-start"
              leftIcon={<Icon as={Plus} boxSize={5} />}
              size="sm"
              borderRadius="md"
              fontWeight="medium"
              onClick={onOpen}
            >
              {t("redditLike.createCommunity", "Create Community")}
            </Button>
            <Button
              variant="ghost"
              justifyContent="flex-start"
              leftIcon={<Icon as={User} boxSize={5} />}
              size="sm"
              borderRadius="md"
              fontWeight="medium"
            >
              {t("redditLike.userSettings", "User Settings")}
            </Button>
          </VStack>
        </Box>

        {/* Main Content */}
        <Box flex={1} p={4} maxW={{ base: "100%", xl: "800px" }} mx="auto">
          {/* Community Header */}
          <Card mb={4} borderColor={borderColor} shadow="sm" overflow="hidden">
            <Box h="80px" bg="blue.500" />
            <CardBody pt={0} pb={4}>
              <Flex mt={-6} mb={3}>
                <Avatar
                  size="lg"
                  name="Programming"
                  bg="cyan.500"
                  color="white"
                  fontSize="xl"
                  border="4px solid white"
                />
              </Flex>
              <Heading size="lg" mb={1}>
                r/programming
              </Heading>
              <Text color={secondaryTextColor} fontSize="sm" mb={4}>
                r/programming • 5.2m {t("redditLike.members", "members")} •
                12.4k {t("redditLike.online", "online")}
              </Text>
              <Text mb={4}>
                {t(
                  "redditLike.communityDescription",
                  "Computer programming is the process of performing a particular computation, usually by designing and building an executable computer program.",
                )}
              </Text>
              <Button colorScheme="blue" size="sm">
                {t("redditLike.join", "Join")}
              </Button>
            </CardBody>
          </Card>

          {/* Post Creation Card */}
          <Card mb={4} borderColor={borderColor} shadow="sm">
            <CardBody p={4}>
              <Flex gap={3}>
                <Avatar size="sm" />
                <Input
                  placeholder={t("redditLike.createPost", "Create Post")}
                  bg={hoverBgColor}
                  borderRadius="full"
                  onClick={onOpen}
                />
                <IconButton
                  aria-label="Add image"
                  icon={<ImageIcon size={18} />}
                  variant="ghost"
                  onClick={onOpen}
                />
                <IconButton
                  aria-label="Add link"
                  icon={<LinkIcon size={18} />}
                  variant="ghost"
                  onClick={onOpen}
                />
              </Flex>
            </CardBody>
          </Card>

          {/* Sorting Options */}
          <Flex mb={4} align="center" justify="space-between">
            <Tabs variant="soft-rounded" colorScheme="blue" size="sm">
              <TabList>
                <Tab onClick={() => setActiveTab("hot")}>
                  {t("redditLike.hot", "Hot")}
                </Tab>
                <Tab onClick={() => setActiveTab("new")}>
                  {t("redditLike.new", "New")}
                </Tab>
                <Tab onClick={() => setActiveTab("top")}>
                  {t("redditLike.top", "Top")}
                </Tab>
                <Tab onClick={() => setActiveTab("rising")}>
                  {t("redditLike.rising", "Rising")}
                </Tab>
              </TabList>
            </Tabs>
            <HStack>
              <Select size="sm" width="120px" borderRadius="md">
                <option value="today">{t("redditLike.today", "Today")}</option>
                <option value="week">
                  {t("redditLike.thisWeek", "This Week")}
                </option>
                <option value="month">
                  {t("redditLike.thisMonth", "This Month")}
                </option>
                <option value="year">
                  {t("redditLike.thisYear", "This Year")}
                </option>
                <option value="allTime">
                  {t("redditLike.allTime", "All Time")}
                </option>
              </Select>
              <IconButton
                aria-label="Filter"
                icon={<Filter size={18} />}
                variant="ghost"
                size="sm"
              />
            </HStack>
          </Flex>

          {/* Post 1 */}
          <Card mb={4} borderColor={borderColor} shadow="sm">
            <CardBody p={0}>
              <Flex>
                {/* Vote buttons */}
                <Flex
                  direction="column"
                  align="center"
                  p={2}
                  bg="gray.50"
                  _dark={{ bg: "gray.800" }}
                  borderTopLeftRadius="md"
                  borderBottomLeftRadius="md"
                  minW="40px"
                >
                  <IconButton
                    aria-label="Upvote"
                    icon={<ArrowUp size={18} />}
                    variant="ghost"
                    size="sm"
                    colorScheme="gray"
                  />
                  <Text fontWeight="bold" my={1}>
                    3.2k
                  </Text>
                  <IconButton
                    aria-label="Downvote"
                    icon={<ArrowDown size={18} />}
                    variant="ghost"
                    size="sm"
                    colorScheme="gray"
                  />
                </Flex>

                {/* Post content */}
                <Box p={3} width="100%">
                  <Flex align="center" mb={2}>
                    <Avatar
                      size="xs"
                      name="Programming"
                      bg="cyan.500"
                      color="white"
                      fontSize="xs"
                      mr={2}
                    />
                    <Text fontSize="sm" fontWeight="medium" mr={1}>
                      r/programming
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      • {t("redditLike.postedBy", "Posted by")} u/devmaster • 5{" "}
                      {t("redditLike.hoursAgo", "hours ago")}
                    </Text>
                  </Flex>

                  <Heading as="h2" size="md" mb={2}>
                    {t(
                      "redditLike.post1Title",
                      "What's your favorite programming language and why?",
                    )}
                  </Heading>

                  <Text mb={4}>
                    {t(
                      "redditLike.post1Content",
                      "I've been coding for about 3 years now, mainly with Python and JavaScript. I'm curious what languages other developers prefer and why. What makes your favorite language special to you? Any languages you tried and absolutely hated?",
                    )}
                  </Text>

                  <Flex align="center" color="gray.500">
                    <Button
                      leftIcon={<MessageSquare size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      142 {t("redditLike.comments", "comments")}
                    </Button>
                    <Button
                      leftIcon={<Award size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      {t("redditLike.award", "Award")}
                    </Button>
                    <Button
                      leftIcon={<Share2 size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      {t("redditLike.share", "Share")}
                    </Button>
                    <Button
                      leftIcon={<MoreHorizontal size={16} />}
                      variant="ghost"
                      size="sm"
                    >
                      {t("redditLike.more", "More")}
                    </Button>
                  </Flex>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          {/* Post 2 */}
          <Card mb={4} borderColor={borderColor} shadow="sm">
            <CardBody p={0}>
              <Flex>
                {/* Vote buttons */}
                <Flex
                  direction="column"
                  align="center"
                  p={2}
                  bg="gray.50"
                  _dark={{ bg: "gray.800" }}
                  borderTopLeftRadius="md"
                  borderBottomLeftRadius="md"
                  minW="40px"
                >
                  <IconButton
                    aria-label="Upvote"
                    icon={<ArrowUp size={18} />}
                    variant="ghost"
                    size="sm"
                    colorScheme="gray"
                  />
                  <Text fontWeight="bold" my={1}>
                    1.8k
                  </Text>
                  <IconButton
                    aria-label="Downvote"
                    icon={<ArrowDown size={18} />}
                    variant="ghost"
                    size="sm"
                    colorScheme="gray"
                  />
                </Flex>

                {/* Post content */}
                <Box p={3} width="100%">
                  <Flex align="center" mb={2}>
                    <Avatar
                      size="xs"
                      name="Programming"
                      bg="cyan.500"
                      color="white"
                      fontSize="xs"
                      mr={2}
                    />
                    <Text fontSize="sm" fontWeight="medium" mr={1}>
                      r/programming
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      • {t("redditLike.postedBy", "Posted by")} u/codeartist •
                      12 {t("redditLike.hoursAgo", "hours ago")}
                    </Text>
                  </Flex>

                  <Heading as="h2" size="md" mb={2}>
                    {t(
                      "redditLike.post2Title",
                      "I built a tool that automatically documents your code using AI",
                    )}
                  </Heading>

                  <Box mb={3} borderRadius="md" overflow="hidden" maxH="500px">
                    <img
                      src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&q=80"
                      alt="Code documentation tool"
                      style={{ width: "100%", objectFit: "cover" }}
                    />
                  </Box>

                  <Text mb={4}>
                    {t(
                      "redditLike.post2Content",
                      "After getting tired of writing documentation, I built this tool that uses AI to analyze your codebase and generate comprehensive documentation. It works with Python, JavaScript, Java, and C#. GitHub repo in comments!",
                    )}
                  </Text>

                  <Flex align="center" color="gray.500">
                    <Button
                      leftIcon={<MessageSquare size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      87 {t("redditLike.comments", "comments")}
                    </Button>
                    <Button
                      leftIcon={<Award size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      {t("redditLike.award", "Award")}
                    </Button>
                    <Button
                      leftIcon={<Share2 size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      {t("redditLike.share", "Share")}
                    </Button>
                    <Button
                      leftIcon={<MoreHorizontal size={16} />}
                      variant="ghost"
                      size="sm"
                    >
                      {t("redditLike.more", "More")}
                    </Button>
                  </Flex>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          {/* Post 3 */}
          <Card mb={4} borderColor={borderColor} shadow="sm">
            <CardBody p={0}>
              <Flex>
                {/* Vote buttons */}
                <Flex
                  direction="column"
                  align="center"
                  p={2}
                  bg="gray.50"
                  _dark={{ bg: "gray.800" }}
                  borderTopLeftRadius="md"
                  borderBottomLeftRadius="md"
                  minW="40px"
                >
                  <IconButton
                    aria-label="Upvote"
                    icon={<ArrowUp size={18} />}
                    variant="ghost"
                    size="sm"
                    colorScheme="gray"
                  />
                  <Text fontWeight="bold" my={1}>
                    956
                  </Text>
                  <IconButton
                    aria-label="Downvote"
                    icon={<ArrowDown size={18} />}
                    variant="ghost"
                    size="sm"
                    colorScheme="gray"
                  />
                </Flex>

                {/* Post content */}
                <Box p={3} width="100%">
                  <Flex align="center" mb={2}>
                    <Avatar
                      size="xs"
                      name="Programming"
                      bg="cyan.500"
                      color="white"
                      fontSize="xs"
                      mr={2}
                    />
                    <Text fontSize="sm" fontWeight="medium" mr={1}>
                      r/programming
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      • {t("redditLike.postedBy", "Posted by")} u/webdev_guru •
                      1 {t("redditLike.dayAgo", "day ago")}
                    </Text>
                  </Flex>

                  <Heading as="h2" size="md" mb={2}>
                    {t(
                      "redditLike.post3Title",
                      "The State of Web Development in 2024 - Trends and Technologies",
                    )}
                  </Heading>

                  <Text mb={4}>
                    {t(
                      "redditLike.post3Content",
                      "I've compiled a comprehensive analysis of web development trends for 2024. Key highlights: 1) AI-assisted development is mainstream, 2) WebAssembly adoption continues to grow, 3) Edge computing is changing how we deploy, 4) Server components are gaining traction. Full article in the link.",
                    )}
                  </Text>

                  <Flex align="center" color="gray.500">
                    <Button
                      leftIcon={<MessageSquare size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      63 {t("redditLike.comments", "comments")}
                    </Button>
                    <Button
                      leftIcon={<Award size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      {t("redditLike.award", "Award")}
                    </Button>
                    <Button
                      leftIcon={<Share2 size={16} />}
                      variant="ghost"
                      size="sm"
                      mr={2}
                    >
                      {t("redditLike.share", "Share")}
                    </Button>
                    <Button
                      leftIcon={<MoreHorizontal size={16} />}
                      variant="ghost"
                      size="sm"
                    >
                      {t("redditLike.more", "More")}
                    </Button>
                  </Flex>
                </Box>
              </Flex>
            </CardBody>
          </Card>

          {/* Load More */}
          <Flex justify="center" my={6}>
            <Button variant="outline" leftIcon={<ChevronUp size={16} />}>
              {t("redditLike.loadMore", "Load More")}
            </Button>
          </Flex>
        </Box>

        {/* Right Sidebar */}
        <Box
          w="300px"
          h="calc(100vh - 56px)"
          borderLeft="1px"
          borderColor={borderColor}
          p={4}
          position="sticky"
          top="56px"
          display={{ base: "none", xl: "block" }}
          overflowY="auto"
        >
          {/* About Community */}
          <Card mb={4} borderColor={borderColor} shadow="sm">
            <CardBody>
              <Heading size="md" mb={3}>
                {t("redditLike.aboutCommunity", "About Community")}
              </Heading>
              <Text fontSize="sm" mb={4}>
                {t(
                  "redditLike.aboutCommunityDesc",
                  "A place for programming and development discussions. Share your knowledge, ask questions, and connect with other developers.",
                )}
              </Text>
              <Flex justify="space-between" mb={4}>
                <Box textAlign="center">
                  <Text fontWeight="bold">5.2m</Text>
                  <Text fontSize="xs" color="gray.500">
                    {t("redditLike.members", "Members")}
                  </Text>
                </Box>
                <Box textAlign="center">
                  <Text fontWeight="bold">12.4k</Text>
                  <Text fontSize="xs" color="gray.500">
                    {t("redditLike.online", "Online")}
                  </Text>
                </Box>
                <Box textAlign="center">
                  <Text fontWeight="bold">2011</Text>
                  <Text fontSize="xs" color="gray.500">
                    {t("redditLike.created", "Created")}
                  </Text>
                </Box>
              </Flex>
              <Divider my={3} />
              <Text fontSize="xs" color="gray.500" mb={1}>
                {t("redditLike.createdDate", "Created Jan 24, 2011")}
              </Text>
              <Button colorScheme="blue" size="sm" width="full" mb={2}>
                {t("redditLike.join", "Join")}
              </Button>
              <Button variant="outline" size="sm" width="full">
                {t("redditLike.createPost", "Create Post")}
              </Button>
            </CardBody>
          </Card>

          {/* Community Rules */}
          <Card mb={4} borderColor={borderColor} shadow="sm">
            <CardBody>
              <Heading size="md" mb={3}>
                {t("redditLike.rules", "r/programming Rules")}
              </Heading>
              <VStack align="stretch" spacing={3}>
                <Box>
                  <Text fontWeight="bold" fontSize="sm">
                    1. {t("redditLike.rule1", "Keep submissions on topic")}
                  </Text>
                </Box>
                <Box>
                  <Text fontWeight="bold" fontSize="sm">
                    2. {t("redditLike.rule2", "No self-promotion")}
                  </Text>
                </Box>
                <Box>
                  <Text fontWeight="bold" fontSize="sm">
                    3. {t("redditLike.rule3", "No memes or image macros")}
                  </Text>
                </Box>
                <Box>
                  <Text fontWeight="bold" fontSize="sm">
                    4. {t("redditLike.rule4", "No job postings")}
                  </Text>
                </Box>
                <Box>
                  <Text fontWeight="bold" fontSize="sm">
                    5. {t("redditLike.rule5", "Be respectful")}
                  </Text>
                </Box>
              </VStack>
              <Button variant="ghost" size="sm" width="full" mt={3}>
                {t("redditLike.viewAll", "View All Rules")}
              </Button>
            </CardBody>
          </Card>

          {/* Moderators */}
          <Card mb={4} borderColor={borderColor} shadow="sm">
            <CardBody>
              <Heading size="md" mb={3}>
                {t("redditLike.moderators", "Moderators")}
              </Heading>
              <VStack align="stretch" spacing={2}>
                <Flex align="center">
                  <Avatar size="xs" mr={2} />
                  <Text fontSize="sm">u/programming_mod</Text>
                </Flex>
                <Flex align="center">
                  <Avatar size="xs" mr={2} />
                  <Text fontSize="sm">u/codemaster</Text>
                </Flex>
                <Flex align="center">
                  <Avatar size="xs" mr={2} />
                  <Text fontSize="sm">u/devguru</Text>
                </Flex>
              </VStack>
              <Button variant="ghost" size="sm" width="full" mt={3}>
                {t("redditLike.viewAll", "View All Moderators")}
              </Button>
            </CardBody>
          </Card>
        </Box>
      </Flex>

      {/* Create Post Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            {t("redditLike.createNewPost", "Create New Post")}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <FormControl mb={4}>
              <FormLabel>{t("redditLike.title", "Title")}</FormLabel>
              <Input placeholder={t("redditLike.titlePlaceholder", "Title")} />
            </FormControl>

            <FormControl mb={4}>
              <FormLabel>{t("redditLike.content", "Content")}</FormLabel>
              <Tabs variant="enclosed" colorScheme="blue" mb={3}>
                <TabList>
                  <Tab>{t("redditLike.post", "Post")}</Tab>
                  <Tab>{t("redditLike.image", "Image & Video")}</Tab>
                  <Tab>{t("redditLike.link", "Link")}</Tab>
                  <Tab>{t("redditLike.poll", "Poll")}</Tab>
                </TabList>
              </Tabs>
              <Textarea
                placeholder={t(
                  "redditLike.contentPlaceholder",
                  "Text (optional)",
                )}
                minH="200px"
              />
            </FormControl>

            <FormControl mb={4}>
              <FormLabel>{t("redditLike.flair", "Flair")}</FormLabel>
              <Select placeholder={t("redditLike.selectFlair", "Select flair")}>
                <option value="discussion">
                  {t("redditLike.discussion", "Discussion")}
                </option>
                <option value="question">
                  {t("redditLike.question", "Question")}
                </option>
                <option value="tutorial">
                  {t("redditLike.tutorial", "Tutorial")}
                </option>
                <option value="news">{t("redditLike.news", "News")}</option>
                <option value="project">
                  {t("redditLike.project", "Project")}
                </option>
              </Select>
            </FormControl>
          </ModalBody>

          <ModalFooter>
            <Button colorScheme="blue" mr={3}>
              {t("redditLike.post", "Post")}
            </Button>
            <Button variant="ghost" onClick={onClose}>
              {t("redditLike.cancel", "Cancel")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
}
