{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@chakra-ui/react": "^3.19.1", "@emotion/react": "^11.14.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "autoprefixer": "10.4.20", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.468.0", "next": "14.2.23", "next-themes": "^0.2.1", "prettier": "^3.3.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "stripe": "^17.6.0", "tempo-devtools": "^2.0.103", "vaul": "^1.1.2"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "ts-jest": "^29.3.4", "typescript": "^5"}}